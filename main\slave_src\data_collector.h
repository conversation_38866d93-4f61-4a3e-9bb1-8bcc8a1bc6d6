#ifndef DATA_COLLECTOR_H
#define DATA_COLLECTOR_H

#include <stdint.h>
#include "esp_err.h"
#include "rolling_buffer.h"

#ifdef __cplusplus
extern "C"
{
#endif

    /**
     * @brief 数据采集器状态
     */
    typedef enum
    {
        DATA_COLLECTOR_STOPPED = 0, // 停止状态
        DATA_COLLECTOR_RUNNING = 1  // 运行状态
    } data_collector_state_t;

    /**
     * @brief 初始化数据采集器
     * @return ESP_OK 成功，其他值表示失败
     */
    esp_err_t data_collector_init(void);

    /**
     * @brief 反初始化数据采集器
     * @return ESP_OK 成功，其他值表示失败
     */
    esp_err_t data_collector_deinit(void);

    /**
     * @brief 设置采集频率并启动定时采集
     * @param frequency 采集频率 (1-20 Hz)
     * @return ESP_OK 成功，其他值表示失败
     */
    esp_err_t data_collector_start(uint8_t frequency);

    /**
     * @brief 停止定时采集
     * @return ESP_OK 成功，其他值表示失败
     */
    esp_err_t data_collector_stop(void);

    /**
     * @brief 获取当前采集器状态
     * @return 当前状态
     */
    data_collector_state_t data_collector_get_state(void);

    /**
     * @brief 获取当前设置的采集频率
     * @return 采集频率 (Hz)
     */
    uint8_t data_collector_get_frequency(void);

    /**
     * @brief 立即采集一次数据
     * @param data 输出数据指针
     * @return ESP_OK 成功，其他值表示失败
     */
    esp_err_t data_collector_sample_once(sensor_data_t *data);

    /**
     * @brief 从缓冲区获取最新的N组数据
     * @param output 输出数据数组
     * @param n 需要获取的数据组数
     * @return 实际获取的数据组数
     */
    int data_collector_get_latest_data(sensor_data_t *output, int n);

    /**
     * @brief 清空数据缓冲区
     */
    void data_collector_clear_buffer(void);

    /**
     * @brief 获取缓冲区中的数据数量
     * @return 数据数量
     */
    int data_collector_get_buffer_count(void);

    /**
     * @brief 获取采集性能统计
     * @param total_samples 总采集次数
     * @param failed_samples 失败次数
     */
    void data_collector_get_stats(uint32_t *total_samples, uint32_t *failed_samples);

    /**
     * @brief 重置性能统计
     */
    void data_collector_reset_stats(void);

#ifdef __cplusplus
}
#endif

#endif // DATA_COLLECTOR_H
