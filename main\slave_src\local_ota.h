/*
 * Local OTA Control Header
 * GPIO15 controlled WiFi AP and File Server
 */

#pragma once

#include "esp_err.h"

#ifdef __cplusplus
extern "C" {
#endif

/* GPIO pin for OTA control */
#define LOCAL_OTA_GPIO_PIN    18

/* OTA control states */
typedef enum {
    LOCAL_OTA_STATE_DISABLED = 0,  /* GPIO18 low - WiFi AP and file server disabled */
    LOCAL_OTA_STATE_ENABLED = 1    /* GPIO18 high - WiFi AP and file server enabled */
} local_ota_state_t;

/**
 * @brief Initialize local OTA control system
 * 
 * This function initializes GPIO15 for input and starts monitoring task
 * 
 * @return esp_err_t ESP_OK on success
 */
esp_err_t local_ota_init(void);

/**
 * @brief Deinitialize local OTA control system
 * 
 * @return esp_err_t ESP_OK on success
 */
esp_err_t local_ota_deinit(void);

/**
 * @brief Get current OTA state
 * 
 * @return local_ota_state_t Current state based on GPIO15 level
 */
local_ota_state_t local_ota_get_state(void);

/**
 * @brief Start WiFi AP and file server (called when GPIO15 goes high)
 * 
 * @return esp_err_t ESP_OK on success
 */
esp_err_t local_ota_start_services(void);

/**
 * @brief Stop WiFi AP and file server (called when GPIO15 goes low)
 * 
 * @return esp_err_t ESP_OK on success
 */
esp_err_t local_ota_stop_services(void);

#ifdef __cplusplus
}
#endif
